# TODO LIST

- [x] Navbar : Rendre Dynamique le nom du site
- [x] Footer : Rendre Dynamique la date (année) du copyright
- [ ] Page : A propos (Une page qui vous présente de manière stylée #lameilleureversiondemoimeme), template (strict minimum DaisyUI),les datas sont en JS of course
- [ ] Page Contact (Un formulaire de contact sécurisé avec Regex)
- [ ] Page Skills (une page qui liste les skills (cf tab competences cda titre) avec des liens vers les leçons correspondantes)
- [ ] Outil pour Jefff : Une page compteur de mensonge (persistant)
- [ ] Outil pour Jefff : Une page De group Generator (équitable + persistant + import clipBoard)
- [ ] Resoudre problème / Tsunami 🌊 en vue : La navBar et Footer ?  (fonction main.js avec services ?, Custom Element ? lib ?  Myyyyyyssstèèèèèèère)

## Des Idees de pages ou Features

- [ ] README.md : expliquer bien le projet, lien stylé github, stackblitz, codepen
- [ ] Page (/lessons/setup): Setup Vite (la page explique comment démmarrer un projet JS/TS avec Vite parce que c est cool) avec explication des fichiers et des dossiers spécifiques (package.json, vite.config.js, node_modules, dist, src, index.html, main.js, style.css, etc.)
- [ ] IA-PROMPT.md : Centralisation des prompt. (No Cheat JS, plutot pour créer du template HTML avec Classes DaisyUI)
- [ ] IA-AGENTS.md : Centralisation des Agents IA (contexte sur).
- [ ] Page Padero Favor de Responder (XP Social)
- [ ] Faire pieuvre

## Lundi 1/09/2025

- [x] Présentation Javascript
  Stack : NPM, Vite, Tailwind, DaisyUI, DomPurify.
- [X] Rev : Variables
- [X] Rev : Fonctions
- [X] DOM : Events

## Mardi 2/09/2025

- [x] Présentation Nouveau Setup
  Stack : NPM, Vite, Tailwind, DaisyUI, DomPurify.
- [x] Question Random Dom Selectors
- [X] Rev : JSDoc l'art de bien faire des commentaires
- [X] Rev : Fonctions
- [X] DOM : Events

## Mercredi 3/09/2025

- [x] Variables Environnement
- [x] Mettre sur Github
- [x] Installer des librairies
- [x] Rev : Boucles
- [x] Page : Team Dynamique (on a un tableau d'objet en JS on doit l'afficher dans le template)

## Demain Jeudi 4/09/2025

- [x] Gestion erreurs
- [x] Service Imports export
- [x] TP Service : Page Progression (Timeline)
- [x] API
- [ ] Classes
- [ ] DOM : Attack regex
- [ ] Setup : installer UUID (pour générer des id unique)
- [ ] BDD
- [ ] Custom Elements ? BOM Geolocation ?

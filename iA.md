# 📌 Guide d’utilisation de l’IA pour nos projets JS

Dans notre parcours **Concepteur Développeur d’Applications (bac+3)**, nous utilisons des outils modernes comme **npm**, **Vite** et **DaisyUI (basé sur TailwindCSS)**.  
L’objectif principal reste **l’apprentissage et la maîtrise du JavaScript**, la logique applicative et la conception logicielle.  

L’IA (LLM comme ChatGPT, Copilot, etc.) est un **outil d’aide**, pas une solution miracle.  
Ce document fixe les **bonnes pratiques** et centralise des **prompts utiles** pour générer rapidement du code HTML/CSS, sans perdre de temps sur la mise en forme.

---

## ✅ Ce que vous pouvez demander à l’IA

- Générer des **templates HTML** avec **DaisyUI** ou un framework CSS (Bootstrap, Tailwind, etc.).
- Créer des composants d’interface simples (formulaires, cards, navbar, timeline…).
- Proposer des snippets CSS pour des ajustements visuels.
- Expliquer l’utilisation de classes CSS/Tailwind/DaisyUI.
- Donner des exemples de **mise en page responsive**.

## ❌ Ce que vous NE devez pas demander à l’IA

- Écrire la logique **JavaScript** à votre place (fonctions, algorithmes, appels API, gestion d’événements, etc.).
- Générer la **structure complète du projet** ou du code backend.
- Copier-coller du code sans le comprendre.  
  👉 Vous devez être capables d’expliquer chaque ligne utilisée.

---

## 🎯 Objectif pédagogique

- Se **concentrer sur la logique applicative** (JavaScript).
- Gagner du temps sur la partie **UI / style**, qui n’est pas l’objectif central.
- Développer un **esprit critique** vis-à-vis du code généré par l’IA.

---

## 💡 Prompts utiles à réutiliser

### 1. Générer un composant HTML avec DaisyUI

```text
Génère un code HTML d’une **navbar responsive** en utilisant les classes DaisyUI et Tailwind.
Le code doit être propre, minimaliste, et facilement intégrable dans un projet Vite.

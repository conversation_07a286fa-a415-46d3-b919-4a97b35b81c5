# Système de TP Automatisé

Ce système permet de gérer automatiquement la liste des TPs avec une interface moderne utilisant DaisyUI.

## 🚀 Fonctionnalités

- **Génération automatique** des cartes TP avec DaisyUI
- **Filtrage** par difficulté et statut
- **Système de badges** et indicateurs visuels
- **Sauvegarde du progrès** dans localStorage
- **Interface responsive** et moderne
- **Ajout facile** de nouveaux TPs

## 📁 Structure

```
src/pages/tp/
├── index.html          # Page principale des TPs
├── tp.js              # Script principal du système
├── mensonge.html      # TP existant
├── user-profile.html  # TP en développement
└── README.md          # Cette documentation
```

## 🛠️ Utilisation

### Ajouter un nouveau TP

#### Méthode 1: Via la console (rapide)
```javascript
createQuickTP(
    'mon-tp',                    // ID unique
    'Mon Nouveau TP',            // Titre
    'Description du TP',         // Description
    'Débutant',                  // Difficulté (Débutant/Intermédiaire/Avancé)
    '45 min',                    // Durée estimée
    ['JavaScript', 'DOM']        // Topics/sujets
);
```

#### Méthode 2: Modification du fichier tp.js
Ajoutez un nouvel objet dans le tableau `tpData` :

```javascript
{
    id: 'mon-tp',
    title: 'Mon Nouveau TP',
    description: 'Description détaillée du TP',
    difficulty: 'Débutant',
    duration: '45 min',
    topics: ['JavaScript', 'DOM'],
    file: 'mon-tp.html',
    status: 'available',    // available, coming-soon, completed
    badge: 'Nouveau'        // Badge optionnel
}
```

### Statuts disponibles

- `available` : TP disponible et accessible
- `coming-soon` : TP en développement
- `completed` : TP terminé par l'utilisateur

### Niveaux de difficulté

- `Débutant` : Badge vert
- `Intermédiaire` : Badge orange  
- `Avancé` : Badge rouge

## 🎨 Personnalisation

### Couleurs et styles
Les styles utilisent les classes DaisyUI et peuvent être personnalisés via les variables CSS de DaisyUI.

### Ajout de nouveaux filtres
Modifiez la fonction `createFilterBar()` dans tp.js pour ajouter de nouveaux critères de filtrage.

## 📊 Fonctionnalités avancées

### Suivi du progrès
Le système sauvegarde automatiquement :
- Date du dernier accès à chaque TP
- Statut de progression
- Données stockées dans localStorage

### Filtrage intelligent
- Filtrage par difficulté
- Filtrage par statut
- Compteur automatique des TPs

## 🔧 API JavaScript

### Fonctions principales

- `initTPPage()` : Initialise la page
- `createTPCard(tp)` : Crée une carte TP
- `renderTPList(data)` : Affiche la liste des TPs
- `filterTPs(type, value)` : Filtre les TPs
- `addNewTP(tpObject)` : Ajoute un nouveau TP
- `createQuickTP(...)` : Création rapide de TP

### Événements

Le système écoute automatiquement :
- `DOMContentLoaded` : Initialisation
- Changements de filtres
- Clics sur les boutons TP

## 📝 Exemple complet

```javascript
// Ajouter un TP complet
const nouveauTP = {
    id: 'api-weather',
    title: 'TP API Météo',
    description: 'Créer une application météo en utilisant une API externe',
    difficulty: 'Intermédiaire',
    duration: '90 min',
    topics: ['API', 'Fetch', 'JSON', 'Async/Await'],
    file: 'api-weather.html',
    status: 'available',
    badge: 'Populaire'
};

addNewTP(nouveauTP);
```

## 🚀 Prochaines améliorations

- [ ] Système de tags avancé
- [ ] Recherche textuelle
- [ ] Tri par popularité
- [ ] Export/Import de configuration
- [ ] Intégration avec un système de notes

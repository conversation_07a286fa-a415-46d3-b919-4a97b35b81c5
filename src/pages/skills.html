<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Jefff303.js</title>
</head>

<body class="min-h-screen">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">
            <!-- Navbar -->
            <div id="navbar-container"></div>

            <!-- Main Content -->
            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
                <div class="overflow-x-auto">
                    <table class="table table-zebra w-full shadow-xl rounded-xl">
                        <thead class="bg-primary text-primary-content">
                            <tr>
                                <th class="w-16">N° Fiche AT</th>
                                <th>Activités types</th>
                                <th class="w-16">N° Fiche CP</th>
                                <th>Compétences professionnelles</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Bloc 1 -->
                            <tr class="hover">
                                <td rowspan="4" class="align-top text-center font-bold bg-primary/10">1</td>
                                <td rowspan="4" class="align-top font-semibold bg-primary/10">Développer une application sécurisée
                                </td>
                                <td>
                                    <div class="badge badge-primary">1</div>
                                </td>
                                <td>Installer et configurer son environnement de travail en fonction du projet</td>
                            </tr>
                            <tr class="hover">
                                <td>
                                    <div class="badge badge-primary">2</div>
                                </td>
                                <td>Développer des interfaces utilisateur</td>
                            </tr>
                            <tr class="hover">
                                <td>
                                    <div class="badge badge-primary">3</div>
                                </td>
                                <td>Développer des composants métier</td>
                            </tr>
                            <tr class="hover">
                                <td>
                                    <div class="badge badge-primary">4</div>
                                </td>
                                <td>Contribuer à la gestion d’un projet informatique</td>
                            </tr>
            
                            <!-- Bloc 2 -->
                            <tr class="hover">
                                <td rowspan="4" class="align-top text-center font-bold bg-secondary/10">2</td>
                                <td rowspan="4" class="align-top font-semibold bg-secondary/10">Concevoir et développer une
                                    application sécurisée organisée en couches</td>
                                <td>
                                    <div class="badge badge-secondary">5</div>
                                </td>
                                <td>Analyser les besoins et maqueter une application</td>
                            </tr>
                            <tr class="hover">
                                <td>
                                    <div class="badge badge-secondary">6</div>
                                </td>
                                <td>Définir l’architecture logicielle d’une application</td>
                            </tr>
                            <tr class="hover">
                                <td>
                                    <div class="badge badge-secondary">7</div>
                                </td>
                                <td>Concevoir et mettre en place une base de données relationnelle</td>
                            </tr>
                            <tr class="hover">
                                <td>
                                    <div class="badge badge-secondary">8</div>
                                </td>
                                <td>Développer des composants d’accès aux données SQL et NoSQL</td>
                            </tr>
            
                            <!-- Bloc 3 -->
                            <tr class="hover">
                                <td rowspan="3" class="align-top text-center font-bold bg-accent/10">3</td>
                                <td rowspan="3" class="align-top font-semibold bg-accent/10">Préparer le déploiement d’une
                                    application sécurisée</td>
                                <td>
                                    <div class="badge badge-accent">9</div>
                                </td>
                                <td>Préparer et exécuter les plans de tests d’une application</td>
                            </tr>
                            <tr class="hover">
                                <td>
                                    <div class="badge badge-accent">10</div>
                                </td>
                                <td>Préparer et documenter le déploiement d’une application</td>
                            </tr>
                            <tr class="hover">
                                <td>
                                    <div class="badge badge-accent">11</div>
                                </td>
                                <td>Contribuer à la mise en production dans une démarche DevOps</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                </div>

                <!-- Conteneur pour les cards de l'équipe -->
                <!-- <div id="team-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"> -->
                    <!-- Les cards seront générées automatiquement par JavaScript -->
                </div>

            </main>

        <!-- Footer -->
        <div id="footer-container"></div>

        </div>
    </div>
    <script type="module" src="/src/main.js"></script>
</body>

</html>
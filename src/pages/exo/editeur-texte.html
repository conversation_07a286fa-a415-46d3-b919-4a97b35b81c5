<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Leçon DOM Events JavaScript - Jefff303.js</title>
  </head>
  <body class="min-h-screen">
    <div class="drawer min-h-screen">
      <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
      <div class="drawer-content flex flex-col min-h-screen">
        <!-- Navbar -->
        <div class="navbar bg-base-100 shadow-lg sticky top-0 z-50">
          <div class="navbar-start">
            <div class="dropdown">
              <div tabindex="0" role="button" class="btn btn-ghost lg:hidden">
                <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 17 14">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 1h15M1 7h15M1 13h15"/>
                </svg>
              </div>
              <ul tabindex="0" class="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow">
                <li><a href="/">Accueil</a></li>
                <li><a>Services</a></li>
                <li><a>À propos</a></li>
                <li><a>Contact</a></li>
              </ul>
            </div>
            <a href="/" id="site-name-navbar" class="btn btn-ghost text-lg sm:text-xl"></a>
          </div>
          <div class="navbar-center hidden lg:flex">
            <ul class="menu menu-horizontal px-1">
              <li><a class="hover:bg-base-200 transition-colors">Accueil</a></li>
              <li><a href="/src/pages/lessons/" class="hover:bg-base-200 transition-colors">Leçons</a></li>
              <li><a href="/src/pages/exo/" class="hover:bg-base-200 transition-colors">Exercices</a></li>
              <li><a href="/src/pages/tp/" class="hover:bg-base-200 transition-colors">TP</a></li>
              <li><a class="hover:bg-base-200 transition-colors">Contact</a></li>
            </ul>
          </div>
          <div class="navbar-end">
            <a class="btn btn-primary btn-sm sm:btn-md">Connexion</a>
          </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          <!-- Le contenu spécifique de la leçon sera inséré ici -->
          <h1 id="main-title" class="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">Exo Editeur de texte en JavaScript</h1>
          <p class="py-4 sm:py-6 text-sm sm:text-base">Avec addEventListener, vous pouvez écouter et réagir aux événements du DOM, comme le clic, le survol, la saisie, etc.</p>
          <!-- Éditeur de texte avec DaisyUI -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
            <!-- Section Formulaire -->
            <div class="card bg-base-100 shadow-xl">
              <div class="card-body">
                <h2 class="card-title text-2xl mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Éditeur de texte
                </h2>

                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">Votre texte</span>
                    <span class="label-text-alt" id="char-count">0 caractères</span>
                  </label>
                  <textarea
                    id="input-text"
                    class="textarea textarea-bordered textarea-lg h-32 resize-none focus:textarea-primary transition-all duration-200"
                    placeholder="Commencez à taper votre texte ici...&#10;&#10;Votre texte apparaîtra en temps réel dans la zone de prévisualisation à droite."
                  ></textarea>
                  <label class="label">
                    <span class="label-text-alt text-info">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Votre texte est automatiquement sauvegardé
                    </span>
                  </label>
                </div>

                <div class="card-actions justify-end mt-4">
                  <button id="clear-btn" class="btn btn-outline btn-error btn-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Effacer
                  </button>
                  <button id="copy-btn" class="btn btn-outline btn-info btn-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    Copier
                  </button>
                </div>
              </div>
            </div>

            <!-- Section Prévisualisation -->
            <div class="card bg-gradient-to-br from-primary/5 to-secondary/5 shadow-xl border border-primary/20">
              <div class="card-body">
                <h2 class="card-title text-2xl mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  Prévisualisation en temps réel
                </h2>

                <div class="divider divider-primary"></div>

                <div
                  id="renderKey"
                  class="min-h-32 p-4 bg-base-100 rounded-lg border-2 border-dashed border-base-300 text-base-content/70 whitespace-pre-wrap break-words transition-all duration-300 hover:border-primary/50 focus-within:border-primary"
                >
                  <div class="flex flex-col items-center justify-center h-full text-center opacity-50">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <p class="text-sm">Votre texte apparaîtra ici...</p>
                    <p class="text-xs mt-1">Commencez à taper dans la zone de texte</p>
                  </div>
                </div>

                <div class="mt-4 stats stats-horizontal shadow w-full">
                  <div class="stat place-items-center">
                    <div class="stat-title text-xs">Mots</div>
                    <div class="stat-value text-lg" id="word-count">0</div>
                  </div>
                  <div class="stat place-items-center">
                    <div class="stat-title text-xs">Lignes</div>
                    <div class="stat-value text-lg" id="line-count">0</div>
                  </div>
                  <div class="stat place-items-center">
                    <div class="stat-title text-xs">Caractères</div>
                    <div class="stat-value text-lg" id="char-count-display">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Toast pour les notifications -->
          <div id="toast-container" class="toast toast-top toast-end"></div>
        </main>

        <!-- Footer -->
        <footer class="footer footer-center bg-base-200 text-base-content rounded-t-lg p-6 sm:p-10 mt-auto">
          <nav class="grid grid-flow-col gap-2 sm:gap-4 text-xs sm:text-sm">
            <a class="link link-hover">À propos</a>
            <a class="link link-hover">Contact</a>
            <a class="link link-hover hidden sm:inline">Mentions légales</a>
            <a class="link link-hover hidden sm:inline">Politique de confidentialité</a>
          </nav>
          <nav>
            <div class="grid grid-flow-col gap-3 sm:gap-4">
              <a class="hover:scale-110 transition-transform"><svg class="w-5 h-5 sm:w-6 sm:h-6 fill-current" viewBox="0 0 24 24"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path></svg></a>
              <a class="hover:scale-110 transition-transform"><svg class="w-5 h-5 sm:w-6 sm:h-6 fill-current" viewBox="0 0 24 24"><path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"></path></svg></a>
              <a class="hover:scale-110 transition-transform"><svg class="w-5 h-5 sm:w-6 sm:h-6 fill-current" viewBox="0 0 24 24"><path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"></path></svg></a>
            </div>
          </nav>
          <aside>
            <p id="copyright-year" class="text-xs sm:text-sm">Copyright © 2024 - Tous droits réservés par MonSite</p>
          </aside>
        </footer>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <script type="module" src="/src/pages/lessons/lessons.js"></script>
    <script type="module" src="/src/pages/exo/exo.js"></script>
  </body>
</html>

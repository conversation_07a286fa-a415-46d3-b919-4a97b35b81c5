console.log('Exo.js');
//---------------------------------------------------------------------------
//----------------------- EXERCICES DOM EVENTS--------------------------------
//---------------------------------------------------------------------------

// const btnClicMeElement = document.querySelector('#btn-clic-me');
// const inputTextElement = document.querySelector('#input-text');
// const renderKeyElement = document.querySelector('#renderKey');
// inputTextElement.addEventListener('keyup', (eventDuclavier) => {
//     console.log(eventDuclavier);
//     console.log('eventDuclavier.key', eventDuclavier.key);
//     // ok mais bof car car interprète aussi les tab backspace etc...
//     // renderKeyElement.textContent += eventDuclavier.key; 
//     renderKeyElement.textContent += eventDuclavier.target.value;
//     // if(inputTextElement.value.length >= 5){
//     //     btnClicMeElement.disabled = true;
//     // }else{
//     //     btnClicMeElement.disabled = false;
//     // }
//     // Ou version optimisée avec condition ternaire
//     btnClicMeElement.disabled = inputTextElement.value.length >= 5 ? true : false;
// });

// const monTextArea = document.querySelector('#formMessage');
// const monBtn = document.querySelector('#formSubmitBtn');
// console.log(monTextArea);
// console.log(monBtn);

// monTextArea.addEventListener('keyup',(event)=>{
//     // console.log(event);
//     // ? Mode cond ternaires
//     monBtn.disabled = monTextArea.value.length>=5 ? true : false;
//     // ? Mode IF classique 
//     // if(monTextArea.value.length>=5){
//     //    monBtn.disabled = true;
//     // }
//     // else{
//     //     monBtn.disabled = false
//     // }
// });

// ? Code existant pour la gestion dynamique (à conserver pour compatibilité)
// const siteNameNavbarElement = document.getElementById('site-name-navbar');
// console.log(siteNameNavbarElement);
// //!Paranoïa : on vérifie si l'élément est bien sélectionné
// if (siteNameNavbarElement) {
//   siteNameNavbarElement.textContent = siteName;
// }

// ? Exercice : Rendre Dynamique la date (année) du copyright
// const copyrightElement = document.getElementById('copyright-year');
// //!Paranoïa : on vérifie si l'élément est bien sélectionné
// if (copyrightElement) {
//   const currentYear = new Date().getFullYear();
//   copyrightElement.textContent = `Copyright © ${currentYear} - Tous droits réservés par ${siteName}`;
// }


//! Exercice : DOM Events (on click sur le main title cela modifie son texte)
// const mainTitleElement = document.getElementById('main-title');
// console.log(mainTitleElement);
// mainTitleElement.addEventListener('click', () => {
//   mainTitleElement.textContent = 'Trop un truc De Botch le JS 🫠';
// });
//! Exercice : DOM Events (on click sur le main title cela modifie son texte) version avec Booleen
// let isClicked = false;
// mainTitleElement.addEventListener('click', () => {
//     mainTitleElement.textContent = isClicked ? 'Trop un truc De Botch le JS 🫠' : 'Les DOM Events en JavaScript';
//     isClicked = !isClicked;
// })
//---------------------------------------------------------------------------
//----------------------- EXERCICES DOM EVENTS Local Storage ----------------
//---------------------------------------------------------------------------
// const inputDomElement  = document.querySelector('#input-text');
// const renderKeyElementDom = document.querySelector('#renderKey');

// inputDomElement.value = localStorage.getItem('monSuperTexte');
// renderKeyElementDom.textContent = localStorage.getItem('monSuperTexte');

//  inputDomElement.addEventListener('keyup', (event) => {
//     console.log(event);
    
//    //  renderKeyElementDom.textContent += event.target.value;
//        renderKeyElementDom.textContent = inputDomElement.value;

//     localStorage.setItem('monSuperTexte', event.target.value);
//     renderKeyElementDom.innerText = localStorage.getItem('monSuperTexte');
//  });

//  // Sélectionne l'élément <input> dans le DOM avec l'id "input-text"
// const inputDomElement = document.querySelector('#input-text');

// // Sélectionne l'élément qui affichera la valeur saisie, avec l'id "renderKey"
// const renderKeyElementDom = document.querySelector('#renderKey');

// // Récupère la valeur stockée dans le localStorage (clé "monSuperTexte")
// // et l'affiche directement dans le champ input si elle existe
// inputDomElement.value = localStorage.getItem('monSuperTexte');

// // Met également cette valeur dans le texte de l'élément affichage
// renderKeyElementDom.textContent = localStorage.getItem('monSuperTexte');

// // Ajoute un écouteur d'événement sur le champ input
// // "keyup" = déclenché à chaque relâchement de touche
// inputDomElement.addEventListener('keyup', (event) => {
//     // Affiche l'objet de l'événement dans la console (debug)
//     console.log(event);

//     // ⚠️ Ici, on ajoute (concatène) le texte au lieu de le remplacer
//     // Ce n'est probablement pas le comportement attendu
//    //  renderKeyElementDom.textContent += event.target.value;
//     renderKeyElementDom.textContent = inputDomElement.value;

//     // Enregistre la valeur actuelle du champ input dans le localStorage
//     // pour la retrouver même après rechargement de la page
//     localStorage.setItem('monSuperTexte', event.target.value);

//     // Met à jour le texte affiché avec la valeur stockée
//     // (mais cela écrase le précédent textContent)
//     renderKeyElementDom.innerText = localStorage.getItem('monSuperTexte');
// });


// MENSONGE
const btnIncrement = document.querySelector('#increment-btn');
const btnReset = document.querySelector('#reset-btn');
const mensongeCounter = document.querySelector('#mensonge-counter');
const mensongeProgressBar = document.querySelector('#mensonge-progress');
mensongeProgressBar.value = 0;

// Step 1 on arrive dans la page on essais de récupérer le nombre de mensonge dans le localStorage sinon 0 
mensongeCounter.innerText = localStorage.getItem('mensonge-counter') || 0;

btnIncrement.addEventListener('click', () => {
   // console.log('click');
   
   //  let counter = parseInt(mensongeCounter.innerText);
    let counter = mensongeCounter.innerText;
    counter++;
    mensongeCounter.innerText = counter;
    localStorage.setItem('mensonge-counter', counter);
});

btnReset.addEventListener('click', () => {
   alert('Interdit Sale Tricheur !');
});

// MENSONGE avec Progress Bar
// const btnIncrement = document.querySelector('#increment-btn');
// const btnReset = document.querySelector('#reset-btn');
// const mensongeCounter = document.querySelector('#mensonge-counter');
// const mensongeProgressBar = document.querySelector('#mensonge-progress');

// // Fonction pour mettre à jour la progress bar
// function updateProgressBar(counter) {
//     // Calcule le pourcentage (max 100)
//     const progressValue = Math.min((counter / 100) * 100, 100);
//     mensongeProgressBar.value = progressValue;

//     console.log(`Progress bar mise à jour: ${progressValue}%`);
// }

// // Initialisation au chargement de la page
// const savedCounter = parseInt(localStorage.getItem('mensonge-counter')) || 0;
// mensongeCounter.innerText = savedCounter;
// updateProgressBar(savedCounter);

// btnIncrement.addEventListener('click', () => {
//     console.log('click');

//     let counter = parseInt(mensongeCounter.innerText);
//     counter++;
//     mensongeCounter.innerText = counter;
//     localStorage.setItem('mensonge-counter', counter);

//     // Mise à jour de la progress bar
//     updateProgressBar(counter);
// });

// btnReset.addEventListener('click', () => {
//     if (confirm('Êtes-vous sûr de vouloir remettre le compteur à zéro ?')) {
//         mensongeCounter.innerText = 0;
//         localStorage.setItem('mensonge-counter', 0);
//         updateProgressBar(0);
//         console.log('Compteur remis à zéro');
//     }
// });
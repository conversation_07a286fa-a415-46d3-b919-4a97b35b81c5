import { marked } from 'marked';
import DOMPurify from 'dompurify';
// Lessons Event Clavier
const inputTextElement = document.getElementById('input-text');
const renderKeyElement = document.getElementById('renderKey');
// console.log(inputTextElement);
inputTextElement.addEventListener('keyup', (eventDuclavier) => {
    // console.log(eventDuclavier);
    console.log('eventDuclavier.key', eventDuclavier.key);
    renderKeyElement.innerHTML = DOMPurify.sanitize( marked.parse(inputTextElement.value));
    // renderKeyElement.textContent += eventDuclavier.key;
});

//?---------------------------------------------------------------------------
//?----------------------- LESSON VARIABLES ENV-------------------------------
//?---------------------------------------------------------------------------
// On test les variables d'environnement via VITE
console.log(import.meta.env.VITE_SOME_KEY) // "123"
console.log(import.meta.env.DB_PASSWORD) // undefined

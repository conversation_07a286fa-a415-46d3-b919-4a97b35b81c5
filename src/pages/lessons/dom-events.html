<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Leçon DOM Events JavaScript - Jefff303.js</title>
  </head>
  <body class="min-h-screen">
    <div class="drawer min-h-screen">
      <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
      <div class="drawer-content flex flex-col min-h-screen">
        <!-- Navbar -->
        <div class="navbar bg-base-100 shadow-lg sticky top-0 z-50">
          <div class="navbar-start">
            <div class="dropdown">
              <div tabindex="0" role="button" class="btn btn-ghost lg:hidden">
                <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 17 14">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 1h15M1 7h15M1 13h15"/>
                </svg>
              </div>
              <ul tabindex="0" class="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow">
                <li><a href="/">Accueil</a></li>
                <li><a>Services</a></li>
                <li><a>À propos</a></li>
                <li><a>Contact</a></li>
              </ul>
            </div>
            <a href="/" id="site-name-navbar" class="btn btn-ghost text-lg sm:text-xl"></a>
          </div>
          <div class="navbar-center hidden lg:flex">
            <ul class="menu menu-horizontal px-1">
              <li><a class="hover:bg-base-200 transition-colors">Accueil</a></li>
              <li><a href="/src/pages/lessons/" class="hover:bg-base-200 transition-colors">Leçons</a></li>
              <li><a href="/src/pages/exo/" class="hover:bg-base-200 transition-colors">Exercices</a></li>
              <li><a href="/src/pages/tp/" class="hover:bg-base-200 transition-colors">TP</a></li>
              <li><a class="hover:bg-base-200 transition-colors">Contact</a></li>
            </ul>
          </div>
          <div class="navbar-end">
            <a class="btn btn-primary btn-sm sm:btn-md">Connexion</a>
          </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          <!-- Le contenu spécifique de la leçon sera inséré ici -->
          <h1 id="main-title" class="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">Les DOM Events en JavaScript</h1>
          <p class="py-4 sm:py-6 text-sm sm:text-base">Avec addEventListener, vous pouvez écouter et réagir aux événements du DOM, comme le clic, le survol, la saisie, etc.</p>
          <h2>On teste d'espionner le clavier</h2>
          <input type="text" id="input-text" placeholder="Entrez votre texte">
          <div id="renderKey"></div>
          
          <!-- <div class="mockup-code my-6">
            <pre data-prefix="1"><code>// Déclaration d'une variable avec 'var' (ancien)</code></pre>
            <pre data-prefix="2"><code>var age = 30;</code></pre>
            <pre data-prefix="3"><code>console.log(age); // Affiche: 30</code></pre>
            <pre data-prefix="4"><code></code></pre>
            <pre data-prefix="5"><code>// Déclaration d'une variable avec 'let' (moderne, réassignable)</code></pre>
            <pre data-prefix="6"><code>let nom = "Alice";</code></pre>
            <pre data-prefix="7"><code>nom = "Bob"; // On peut réassigner 'nom'</code></pre>
            <pre data-prefix="8"><code>console.log(nom); // Affiche: Bob</code></pre>
            <pre data-prefix="9"><code></code></pre>
            <pre data-prefix="10"><code>// Déclaration d'une constante avec 'const' (moderne, non réassignable)</code></pre>
            <pre data-prefix="11"><code>const PI = 3.14159;</code></pre>
            <pre data-prefix="12"><code>// PI = 3.14; // Ceci provoquerait une erreur: Assignment to constant variable.</code></pre>
            <pre data-prefix="13"><code>console.log(PI); // Affiche: 3.14159</code></pre>
          </div> -->

          <!-- <section class="mt-8">
            <h2 class="text-2xl sm:text-3xl font-semibold mb-4">Types de Déclaration de Variables</h2>
            
            <div class="card bg-base-100 shadow-xl mb-6">
              <div class="card-body">
                <h3 class="card-title text-xl sm:text-2xl">1. <code class="text-primary">var</code> (à éviter)</h3>
                <p>C'est la méthode traditionnelle pour déclarer des variables en JavaScript. Cependant, elle présente des inconvénients comme le <strong>hoisting</strong> et le fait qu'elle est "function-scoped" et non "block-scoped", ce qui peut entraîner des comportements inattendus.</p>
                <div class="mockup-code my-4">
                  <pre data-prefix="1"><code>if (true) {</code></pre>
                  <pre data-prefix="2"><code>  var x = 10;</code></pre>
                  <pre data-prefix="3"><code>}</code></pre>
                  <pre data-prefix="4"><code>console.log(x); // Affiche 10, car 'var' n'est pas limité au bloc 'if'.</code></pre>
                </div>
              </div>
            </div>

            <div class="card bg-base-100 shadow-xl mb-6">
              <div class="card-body">
                <h3 class="card-title text-xl sm:text-2xl">2. <code class="text-secondary">let</code> (préféré)</h3>
                <p><code class="text-secondary">let</code> a été introduit avec ES6 (ECMAScript 2015) pour résoudre les problèmes de <code class="text-primary">var</code>. Les variables déclarées avec <code class="text-secondary">let</code> sont <strong>block-scoped</strong> et ne sont pas soumises au même type de hoisting que <code class="text-primary">var</code>. Elles peuvent être réassignées.</p>
                <div class="mockup-code my-4">
                  <pre data-prefix="1"><code>if (true) {</code></pre>
                  <pre data-prefix="2"><code>  let y = 20;</code></pre>
                  <pre data-prefix="3"><code>  console.log(y); // Affiche 20</code></pre>
                  <pre data-prefix="4"><code>}</code></pre>
                  <pre data-prefix="5"><code>// console.log(y); // Erreur: y is not defined, car 'let' est block-scoped.</code></pre>
                </div>
              </div>
            </div>

            <div class="card bg-base-100 shadow-xl mb-6">
              <div class="card-body">
                <h3 class="card-title text-xl sm:text-2xl">3. <code class="text-accent">const</code> (préféré pour les valeurs fixes)</h3>
                <p><code class="text-accent">const</code> est également introduit avec ES6. Les constantes sont <strong>block-scoped</strong> et, une fois qu'une valeur leur est assignée, elles ne peuvent pas être réassignées. Cela ne signifie pas que la valeur est immuable, mais plutôt que la référence à cette valeur ne peut pas changer (important pour les objets et les tableaux).</p>
                <div class="mockup-code my-4">
                  <pre data-prefix="1"><code>const utilisateur = { nom: "Alice" };</code></pre>
                  <pre data-prefix="2"><code>utilisateur.nom = "Bob"; // Ceci est permis car l'objet lui-même est modifié, pas la référence.</code></pre>
                  <pre data-prefix="3"><code>console.log(utilisateur.nom); // Affiche: Bob</code></pre>
                  <pre data-prefix="4"><code></code></pre>
                  <pre data-prefix="5"><code>// utilisateur = { nom: "Charlie" }; // Erreur: Assignment to constant variable.</code></pre>
                </div>
              </div>
            </div>
          </section> -->

          <!-- <section class="mt-8">
            <h2 class="text-2xl sm:text-3xl font-semibold mb-4">Portée (Scope) des Variables</h2>
            <div class="card bg-base-100 shadow-xl mb-6">
              <div class="card-body">
                <h3 class="card-title text-xl sm:text-2xl">Portée Globale</h3>
                <p>Une variable déclarée en dehors de toute fonction ou bloc a une portée globale et est accessible de partout dans le code.</p>
                <div class="mockup-code my-4">
                  <pre data-prefix="1"><code>var globale = "Je suis globale";</code></pre>
                  <pre data-prefix="2"><code>function montrerGlobale() {</code></pre>
                  <pre data-prefix="3"><code>  console.log(globale); // Accessible</code></pre>
                  <pre data-prefix="4"><code>}</code></pre>
                  <pre data-prefix="5"><code>montrerGlobale();</code></pre>
                </div>
              </div>
            </div>

            <div class="card bg-base-100 shadow-xl mb-6">
              <div class="card-body">
                <h3 class="card-title text-xl sm:text-2xl">Portée de Fonction (Function Scope)</h3>
                <p>Les variables déclarées avec <code class="text-primary">var</code> à l'intérieur d'une fonction ont une portée de fonction. Elles ne sont accessibles qu'à l'intérieur de cette fonction.</p>
                <div class="mockup-code my-4">
                  <pre data-prefix="1"><code>function maFonction() {</code></pre>
                  <pre data-prefix="2"><code>  var locale = "Je suis locale";</code></pre>
                  <pre data-prefix="3"><code>  console.log(locale);</code></pre>
                  <pre data-prefix="4"><code>}</code></pre>
                  <pre data-prefix="5"><code>maFonction(); // Affiche: Je suis locale</code></pre>
                  <pre data-prefix="6"><code>// console.log(locale); // Erreur: locale is not defined</code></pre>
                </div>
              </div>
            </div>

            <div class="card bg-base-100 shadow-xl mb-6">
              <div class="card-body">
                <h3 class="card-title text-xl sm:text-2xl">Portée de Bloc (Block Scope)</h3>
                <p>Les variables déclarées avec <code class="text-secondary">let</code> et <code class="text-accent">const</code> à l'intérieur d'un bloc (délimité par des accolades <code class="text-info">{}</code>, comme un <code class="text-info">if</code>, <code class="text-info">for</code>, ou <code class="text-info">while</code>) ont une portée de bloc. Elles ne sont accessibles qu'à l'intérieur de ce bloc.</p>
                <div class="mockup-code my-4">
                  <pre data-prefix="1"><code>if (true) {</code></pre>
                  <pre data-prefix="2"><code>  let blockVar = "Je suis de portée bloc";</code></pre>
                  <pre data-prefix="3"><code>  const blockConst = "Moi aussi";</code></pre>
                  <pre data-prefix="4"><code>  console.log(blockVar); // Accessible</code></pre>
                  <pre data-prefix="5"><code>  console.log(blockConst); // Accessible</code></pre>
                  <pre data-prefix="6"><code>}</code></pre>
                  <pre data-prefix="7"><code>// console.log(blockVar); // Erreur: blockVar is not defined</code></pre>
                  <pre data-prefix="8"><code>// console.log(blockConst); // Erreur: blockConst is not defined</code></pre>
                </div>
              </div>
            </div>
          </section> -->

          <!-- <div class="alert alert-info mt-8">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
            <span><strong>Bonne pratique :</strong> Utilisez toujours <code class="text-secondary">let</code> ou <code class="text-accent">const</code> pour déclarer vos variables. Évitez <code class="text-primary">var</code> pour prévenir des erreurs courantes et améliorer la lisibilité du code.</span>
          </div> -->

        </main>

        <!-- Footer -->
        <footer class="footer footer-center bg-base-200 text-base-content rounded-t-lg p-6 sm:p-10 mt-auto">
          <nav class="grid grid-flow-col gap-2 sm:gap-4 text-xs sm:text-sm">
            <a class="link link-hover">À propos</a>
            <a class="link link-hover">Contact</a>
            <a class="link link-hover hidden sm:inline">Mentions légales</a>
            <a class="link link-hover hidden sm:inline">Politique de confidentialité</a>
          </nav>
          <nav>
            <div class="grid grid-flow-col gap-3 sm:gap-4">
              <a class="hover:scale-110 transition-transform"><svg class="w-5 h-5 sm:w-6 sm:h-6 fill-current" viewBox="0 0 24 24"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path></svg></a>
              <a class="hover:scale-110 transition-transform"><svg class="w-5 h-5 sm:w-6 sm:h-6 fill-current" viewBox="0 0 24 24"><path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"></path></svg></a>
              <a class="hover:scale-110 transition-transform"><svg class="w-5 h-5 sm:w-6 sm:h-6 fill-current" viewBox="0 0 24 24"><path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"></path></svg></a>
            </div>
          </nav>
          <aside>
            <p id="copyright-year" class="text-xs sm:text-sm">Copyright © 2024 - Tous droits réservés par MonSite</p>
          </aside>
        </footer>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <script type="module" src="/src/pages/lessons/lessons.js"></script>
  </body>
</html>

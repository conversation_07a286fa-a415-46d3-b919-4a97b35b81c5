import DOMPurify from 'dompurify';
import collection from 'easter-egg-collection';
import { initNavbar, initFooter } from './services/uiService.js';
import { renderTimeline } from './services/timelineUiService.js';
// import { moduleHello } from './services/helloService.js';
//  moduleHello('jojo');

// ? Configuration du site
const siteName = "Jefff.js";
// Note: Ces fonctions seront utilisées quand la navbar et le footer seront retirés du HTML
// et remplacés par des conteneurs vides
// Exemple d'utilisation future :
document.addEventListener('DOMContentLoaded', () => {
    initNavbar('#navbar-container', siteName);
    initFooter('#footer-container', siteName);
    renderTimeline('#timeline');
  });


// On test les variables d'environnement via VITE
console.log(import.meta.env.VITE_SOME_KEY) // "123"
console.log(import.meta.env.DB_PASSWORD) // undefined

//* On est dans /src/main.js


// ? Initialisation de l'interface utilisateur

// ? Code existant pour la gestion dynamique (à conserver pour compatibilité)
const siteNameNavbarElement = document.getElementById('site-name-navbar');
console.log(siteNameNavbarElement);
//!Paranoïa : on vérifie si l'élément est bien sélectionné
if (siteNameNavbarElement) {
  siteNameNavbarElement.textContent = siteName;
}

// ? Exercice : Rendre Dynamique la date (année) du copyright
const copyrightElement = document.getElementById('copyright-year');
//!Paranoïa : on vérifie si l'élément est bien sélectionné
if (copyrightElement) {
  const currentYear = new Date().getFullYear();
  copyrightElement.textContent = `Copyright © ${currentYear} - Tous droits réservés par ${siteName}`;
}
//---------------------------------------------------------------------------
//----------------------- LESSON DOM EVENTS--------------------------------
//---------------------------------------------------------------------------
//! Exercice : DOM Events (on click sur le main title cela modifie son texte)
const mainTitleElement = document.getElementById('main-title');
// console.log(mainTitleElement);
// mainTitleElement.addEventListener('click', () => {
//   mainTitleElement.textContent = 'Trop un truc De Botch le JS 🫠';
// });
//! Exercice : DOM Events (on click sur le main title cela modifie son texte) version avec Booleen
// let isClicked = false;
// mainTitleElement.addEventListener('click', () => {
//     mainTitleElement.textContent = isClicked ? 'Trop un truc De Botch le JS 🫠' : 'Les DOM Events en JavaScript';
//     isClicked = !isClicked;
// })







/**
 * Service pour gérer les éléments d'interface utilisateur (navbar, footer)
 */

/**
 * Fonction pour détecter la page actuelle
 * @returns {string} Le nom de la page actuelle
 */
export function getCurrentPage() {
    const path = window.location.pathname;

    // Si on est sur la page team
    if (path.includes('team.html') || path.includes('/team/')) {
        return 'team';
    }

    // Si on est sur la page d'accueil
    if (path === '/' || path.includes('index.html')) {
        return 'home';
    }

    // Autres pages
    if (path.includes('contact.html')) return 'contact';
    if (path.includes('timeline.html')) return 'timeline';
    if (path.includes('skills.html')) return 'skills';
    if (path.includes('about.html')) return 'about';
    if (path.includes('lessons/')) return 'lessons';
    if (path.includes('exo/')) return 'exercises';
    if (path.includes('tp/')) return 'tp';

    return 'unknown';
}

/**
 * Crée et retourne l'élément navbar
 * @param {string} siteName - Le nom du site à afficher
 * @returns {HTMLElement} L'élément navbar
 */
export function createNavbar(siteName = "Jefff.js") {    
  const navbar = document.createElement('div');
  navbar.className = 'navbar bg-base-100 shadow-lg sticky top-0 z-50';

  navbar.innerHTML = `
    <div class="navbar-start">
      <div class="dropdown">
        <div tabindex="0" role="button" class="btn btn-ghost lg:hidden">
          <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 17 14">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 1h15M1 7h15M1 13h15"/>
          </svg>
        </div>
        <ul tabindex="0" class="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow">
          <li><a href="/">Accueil</a></li>
          <li><a href="/src/pages/lessons/" class="hover:bg-base-200 transition-colors">Leçons</a></li>
          <li><a href="/src/pages/exo/" class="hover:bg-base-200 transition-colors">Exercices</a></li>
          <li><a href="/src/pages/tp/" class="hover:bg-base-200 transition-colors">TP</a></li>
          <li><a>À propos</a></li>
          <li><a>Contact</a></li>
        </ul>
      </div>
      <a href="/" id="site-name-navbar" class="btn btn-ghost text-lg sm:text-xl">${siteName}</a>
    </div>
    <div class="navbar-center hidden lg:flex">
      <ul class="menu menu-horizontal px-1">
        <li><a href="/" class="hover:bg-base-200 transition-colors">Accueil</a></li>
        <li><a href="/src/pages/lessons/" class="hover:bg-base-200 transition-colors">Leçons</a></li>
        <li><a href="/src/pages/exo/" class="hover:bg-base-200 transition-colors">Exercices</a></li>
        <li><a href="/src/pages/tp/" class="hover:bg-base-200 transition-colors">TP</a></li>
        <li><a href="/src/pages/team.html" class="hover:bg-base-200 transition-colors">Team</a></li>
        <li><a href="/src/pages/timeline.html" class="hover:bg-base-200 transition-colors">Timeline</a></li>
        <li><a href="/src/pages/contact.html" class="hover:bg-base-200 transition-colors">Contact</a></li>
      </ul>
    </div>
    <div class="navbar-end">
      <a class="btn btn-primary btn-sm sm:btn-md">Connexion</a>
    </div>
  `;

  return navbar;
}

/**
 * Crée et retourne l'élément footer
 * @param {string} siteName - Le nom du site à afficher dans le copyright
 * @param {number} year - L'année pour le copyright (par défaut: année actuelle)
 * @returns {HTMLElement} L'élément footer
 */
export function createFooter(siteName = "MonSite", year = new Date().getFullYear()) {
  const footer = document.createElement('footer');
  footer.className = 'footer footer-center bg-base-200 text-base-content rounded-t-lg p-6 sm:p-10 mt-auto';

  footer.innerHTML = `
    <nav class="grid grid-flow-col gap-2 sm:gap-4 text-xs sm:text-sm">
      <a class="link link-hover">À propos</a>
      <a href="/src/pages/skills.html" class="link link-hover">Skills</a>
      <a class="link link-hover">Contact</a>
      <a class="link link-hover hidden sm:inline">Mentions légales</a>
      <a class="link link-hover hidden sm:inline">Politique de confidentialité</a>
    </nav>
    <nav>
      <div class="grid grid-flow-col gap-3 sm:gap-4">
        <a class="hover:scale-110 transition-transform">
          <svg class="w-5 h-5 sm:w-6 sm:h-6 fill-current" viewBox="0 0 24 24">
            <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path>
          </svg>
        </a>
        <a class="hover:scale-110 transition-transform">
          <svg class="w-5 h-5 sm:w-6 sm:h-6 fill-current" viewBox="0 0 24 24">
            <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"></path>
          </svg>
        </a>
        <a class="hover:scale-110 transition-transform">
          <svg class="w-5 h-5 sm:w-6 sm:h-6 fill-current" viewBox="0 0 24 24">
            <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"></path>
          </svg>
        </a>
      </div>
    </nav>
    <aside>
      <p id="copyright-year" class="text-xs sm:text-sm">Copyright © ${year} - Tous droits réservés par ${siteName}</p>
    </aside>
  `;

  return footer;
}

/**
 * Initialise la navbar dans un conteneur spécifique
 * @param {string|HTMLElement} container - Le sélecteur CSS ou l'élément conteneur
 * @param {string} siteName - Le nom du site
 */
export function initNavbar(container, siteName = "Jefff.js") {
  const containerElement = typeof container === 'string'
    ? document.querySelector(container)
    : container;

  if (containerElement) {
    const navbar = createNavbar(siteName);
    containerElement.appendChild(navbar);
  } else {
    console.warn('Conteneur navbar non trouvé:', container);
  }
}

/**
 * Initialise le footer dans un conteneur spécifique
 * @param {string|HTMLElement} container - Le sélecteur CSS ou l'élément conteneur
 * @param {string} siteName - Le nom du site
 * @param {number} year - L'année pour le copyright
 */
export function initFooter(container, siteName = "MonSite", year = new Date().getFullYear()) {
  const containerElement = typeof container === 'string'
    ? document.querySelector(container)
    : container;

  if (containerElement) {
    const footer = createFooter(siteName, year);
    containerElement.appendChild(footer);
  } else {
    console.warn('Conteneur footer non trouvé:', container);
  }
}
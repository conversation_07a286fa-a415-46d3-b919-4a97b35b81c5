// src/services/timelineService.js
import { timelineSteps } from './timelineDataService.js';
import DOMPurify from 'dompurify';

export function renderTimeline(containerId) {
    const timelineEl = document.querySelector(containerId);
    console.log(timelineEl);

    try {
        // Génération dynamique des items tkt on DOMPurifie le innerHTML
        timelineEl.innerHTML =DOMPurify.sanitize( timelineSteps.map((item, index) => `
      <li>
        ${index !== 0 ? "<hr />" : ""}
        <div class="timeline-middle">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
               fill="currentColor" class="h-5 w-5">
            <path fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
                  clip-rule="evenodd" />
          </svg>
        </div>
        <div class="timeline-${item.side} mb-10 ${item.side === "start" ? "md:text-end" : ""}">
          <time class="font-mono italic">${item.year}</time>
          <div class="text-lg font-black">${item.title}</div>
          ${item.text}
        </div>
      </li>
    `).join(""));
    } catch (error) {
        console.log("Erreur lors du rendu de la timeline: faudrais optimiser", error);
        if (timelineEl) {
            timelineEl.innerHTML = "<p>Désolé, la timeline n'a pas pu être chargée.</p>";
        }
    }
}